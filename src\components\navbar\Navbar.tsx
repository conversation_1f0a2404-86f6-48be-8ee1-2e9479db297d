// src/components/Navbar.tsx
import { NavLink } from "react-router-dom";

export default function Navbar() {

  const navItems = [
    { name: "Home", path: "/" },
    { name: "Insights", path: "/insights" },
    { name: "Notifications", path: "/notifications" },
    { name: "Profile", path: "/profile" },
  ];
  return (
    <>
      <nav className="bg-custom-nav bg-no-repeat bg-[length:100%_100%] rounded-lg flex items-center justify-between shadow-lg">
        {/* navbar content */}

        {/* Logo */}
        <div className="flex items-center space-x-2">
          <img src="/brandLogo.svg" alt="brand-logo" />
        </div>

        {/* Nav Links */}
        <ul className="flex space-x-8">
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `inline-flex items-center h-full py-5 px-3 text-white font-medium transition-all duration-300 border-b-4 ${
                  isActive
                    ? "border-green-400  bg-[#0F2027] font-bold"
                    : "border-transparent hover:border-green-300 hover:bg-[#0F2027]"
                }`
              }
            >
              {item.name}
            </NavLink>
          ))}
        </ul>

        {/* Button */}
        <button
        type="button"
          className="custom-cyan-btn bg-gradient-to-r font-semibold py-2 px-4 rounded-lg shadow hover:opacity-90 transition"
          // onClick={() => setIsModalOpen(true)}
        >
          + Add Expense
        </button>
      </nav>

      {/* <ExpenseModal
        modalOpen={isModalOpen}
        setModalOpen={setIsModalOpen}
        expenses={expenses}
        setExpenses={setExpenses}
      /> */}
    </>
  );
}
