
import React from "react";
import type { ReactNode } from "react";

export interface CardProps {
  title?: string;
  description?: string;
  icon?: ReactNode;
  className?: string;
  onClick?: () => void;
  children?: ReactNode;
}

const Card: React.FC<CardProps> = ({
  title,
  description,
  icon,
  className = "",
  onClick,
  children,
}) => {
  return (
    <div
      onClick={onClick}
      className={`rounded-2xl shadow-md p-6 bg-white hover:shadow-lg transition-all ${
        onClick ? "cursor-pointer" : ""
      } flex items-start gap-4 ${className}`}
    >
      {icon && (
        <div className="text-3xl text-blue-500 shrink-0">
          {icon}
        </div>
      )}
      <div className="flex flex-col gap-1">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
        {children && <div className="mt-2">{children}</div>}
      </div>
    </div>
  );
};

export default Card;
