import { RiBar<PERSON>hartFill } from "react-icons/ri";
import { FaRegClock } from "react-icons/fa";
import { FaRupeeSign, FaReceipt } from 'react-icons/fa';
import VerticalDivider from "../../components/VerticalDivider";

import { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, ReferenceLine, Tooltip } from 'recharts';
import { FaCaretUp } from "react-icons/fa";
import { FaRegCalendar } from "react-icons/fa";
import { FaCheck } from "react-icons/fa6";
import Card from "../../components/Card";



const data = [
  { month: 'SEP', value: 115, budget: 105 },
  { month: 'OCT', value: 95, budget: 105 },
  { month: 'NOV', value: 1065, budget: 115 },
  { month: 'DEC', value: 95, budget: 105 },
  { month: 'JAN', value: 110, budget: 120 },
  { month: 'FEB', value: 115, budget: 125 }
];




const Home = () => {

  return (

    <div className="h-screen w-screen p-5">

      <div className="grid grid-cols-2 auto-rows-auto gap-4">
        {/* card */}

        <div className="grid grid-cols-3 gap-4">

          <div className="bg-white rounded-xl shadow p-5 flex items-center gap-4">
            <div className="w-12 h-12 flex items-center justify-center rounded-full bg-orange-100">
              <RiBarChartFill className="text-orange-700" size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-bold">Pending</p>
              <p className="text-xl font-bold text-gray-700">4</p>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow p-5 flex items-center gap-4">
            <div className="w-12 h-12 flex items-center justify-center rounded-full bg-green-100">
              <RiBarChartFill className="text-green-700" size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-bold">Approved</p>
              <p className="text-xl font-bold text-gray-700">15</p>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow p-5 flex items-center gap-4">
            <div className="w-12 h-12 flex items-center justify-center rounded-full bg-red-100">
              <RiBarChartFill className="text-red-700" size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 font-bold">Rejected</p>
              <p className="text-xl font-bold text-gray-700">6</p>
            </div>
          </div>

        </div>

        <div className="max-w-full bg-white rounded-xl shadow p-6 flex flex-col md:flex-row md:space-x-10 space-y-6 md:space-y-0">
          <div className="flex-1 flex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <div className="w-10 h-10 flex items-center justify-center gap-5 rounded-xl bg-sky-900">
                <img src="/homePageCommanCategoryIcon.svg" alt="brand-logo" />
              </div>
              <p className="text-sm font-medium text-gray-500">
                Common Category Expenditure
              </p>
            </div>
            <p className="text-sm font-bold text-gray-900">
              Travel & Transportation
            </p>
          </div>

          <VerticalDivider />

          <div className="flex-1 flex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <div className="w-10 h-10 flex items-center justify-center gap-5 rounded-xl bg-sky-900">
                <FaRegClock className="text-white text-2xl" />
              </div>
              <p className="text-sm font-medium text-gray-500">
                Average Approval Time
              </p>
            </div>
            <p className="text-sm font-bold text-gray-900">
              Your claims are usually approved in{" "}
              <span className="text-cyan-600 font-semibold">1 to 2</span> days
            </p>
          </div>
        </div>




        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          {/* Usage Limit */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '20%' }}></div>
          </div>
          <div className="flex justify-between items-center mt-4">
            <span className="text-gray-500 font-bold">Usage Limit</span>
            <span className="text-gray-500 font-semibold">$350.60 / $50000</span>
          </div>
        </div>



        <div className="bg-white rounded-2xl p-6 shadow-lg row-span-2">
          {/* Header */}
          <div className="flex items-center justify-between mb-12">
            <div className="text-md font-bold text-gray-800">Historical Trend</div>
            <div className="flex items-center gap-3">
              <button
                className="px-5 py-2.5 rounded-lg text-xs text-gray-400 flex items-center gap-3 bg-blue-50"
                onClick={() => { }}
              >
                <FaRegCalendar className="w-4 h-4 text-gray-400" />
                This Year
              </button>
              <button
                className="p-2 rounded-lg text-gray-500 bg-blue-50"
                onClick={() => { }}
              >
                <RiBarChartFill className="w-4 h-4 text-teal-600" />
              </button>
            </div>
          </div>

          {/* Metrics */}
          <div className="mb-8">
            <div className="mb-2">
              <span className="text-3xl font-bold text-gray-500">$37.5K</span>
            </div>
            <div className="flex items-baseline gap-3">
              <div className="text-sm text-gray-400 mb-4 font-semibold">Total Spent</div>
              <div className="text-sm text-green-400 font-bold flex items-center gap-1">
                <FaCaretUp />
                <span>
                  +2.45%
                </span>

              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-400 p-1 rounded-full flex items-center justify-center">
                <FaCheck className="text-white w-3 h-3" />
              </div>
              <span className="text-md font-bold text-green-400">On track</span>
            </div>



          </div>

          {/* Chart */}
          <div className="h-48 relative">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#9CA3AF' }}
                  dy={10}
                />
                <YAxis hide />

                {/* Budget line (lighter) */}
                <Line
                  type="monotone"
                  dataKey="budget"
                  stroke="#7DD3FC"
                  strokeWidth={2}
                  dot={false}
                  strokeDasharray="none"
                />

                {/* Actual spending line (darker) */}
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#0891B2"
                  strokeWidth={3}
                  dot={false}
                />

                {/* Highlight point for November */}
                <ReferenceLine x="NOV" stroke="none">
                  <circle cx="0" cy="0" r="4" fill="#0891B2" />
                </ReferenceLine>

                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-teal-500 text-white px-3 py-1 rounded-lg text-sm font-medium relative">
                          <div className="flex flex-col gap-1">
                            <div>Actual: ${payload[0].value}.00</div>
                            <div>Budget: ${payload[1].value}.00</div>
                          </div>
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-teal-500"></div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />

              </LineChart>
            </ResponsiveContainer>

            {/* Tooltip for November */}


            
          </div>
        </div>





        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="text-md font-bold text-gray-800 mb-5">
                Latest Reimbursement Request
              </div>
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-gray-600">
                  Travel to client location
                </div>
                <div className="text-gray-400 text-2xl">12/12/2022</div>
              </div>
            </div>
            <div className="brand-gradient text-white px-12 py-3 rounded-full text-xs font-medium">
              Submitted
            </div>
          </div>

          {/* Amount and Type */}
          <div className="flex items-center gap-8 mb-12 mt-6">
            <div className="flex items-center gap-2">
              <div className="latest-reimbursement-card-gradient p-2 rounded-lg">
                <FaRupeeSign className="text-white text-lg" />
              </div>
              <span className="text-lg font-bold text-gray-900">8000.00</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="latest-reimbursement-card-gradient p-2 rounded-lg">
                <FaReceipt className="text-white text-lg" />
              </div>
              <span className="text-gray-900 font-bold">Multiple Expenses</span>
            </div>
          </div>

          {/* Expense Categories */}
          <div className="mb-6 bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-3 h-8">
              <span className="text-gray-500 font-medium">Expense Categories</span>
              <VerticalDivider />
              <div className="flex gap-2">
                <img src="palneIcon.svg" className="text-white text-sm" />
                <img src="burgerIcon.svg" className="text-white text-sm" />
              </div>
            </div>
          </div>


          {/* Approval Workflow */}
          <div className="bg-teal-800 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 text-white">
                <img src="successIconGreen.svg" />
                <span className="font-medium">Manager</span>
              </div>

              <VerticalDivider />

              <div className="flex items-center gap-2 text-white">
                <img src="successIconWhite.svg" />
                <span className="font-medium">Finance Dept</span>
              </div>

              <VerticalDivider />

              <div className="flex items-center gap-2 text-white">
                <img src="successIconWhite.svg" />
                <span className="font-medium">Payment Release</span>
              </div>
            </div>
          </div>


        </div>


      </div>








    </div>


  )
}

export default Home
