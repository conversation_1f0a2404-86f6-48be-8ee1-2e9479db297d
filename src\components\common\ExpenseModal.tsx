import React from "react";
import type { FC } from "react";

interface Expense {
  name: string;
  date: string;
  amount: number;
  category: string;
  description: string;
  receipts: string[];
}

interface ExpenseModalProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  expenses: Expense[];
  setExpenses: React.Dispatch<React.SetStateAction<Expense[]>>;
}

const ExpenseModal: FC<ExpenseModalProps> = ({
  modalOpen,
  setModalOpen,
  expenses,
  setExpenses,
}) => {
  const handleAddExpense = (event: React.FormEvent) => {
    event.preventDefault();
    const newExpense: Expense = {
      name: "Food for client meeting",
      date: "12/12/2022",
      amount: 4000,
      category: "Meals & Entertainment",
      description:
        "Exploring the world through travel and transportation opens up a realm of possibilities. Whether it's hopping on a plane to a distant country, taking a scenic...",
      receipts: ["receipt1.jpg", "receipt2.jpg", "receipt3.jpg"],
    };
    setExpenses([...expenses, newExpense]);
  };

  return (
    <div
      className={`fixed inset-0 bg-[rgba(0,0,0,0.66)] backdrop-blur-[6.5px] flex items-center justify-center z-50 ${
        modalOpen ? "block" : "hidden"
      }`}
    >
      <div className="bg-white rounded-lg p-8 shadow-lg w-[800px]">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-[#1D3557]">Add New Expense</h2>
          <button
            className="text-red-500 font-semibold hover:text-red-700"
            onClick={() => setModalOpen(false)}
          >
            Close
          </button>
        </div>

        {/* Display Existing Expenses */}
        {expenses.map((expense, index) => (
          <div
            key={index}
            className="border rounded-lg p-4 mb-4 bg-gray-100 shadow-sm"
          >
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold text-lg">{expense.name}</h3>
                <p className="text-sm text-gray-500">{expense.date}</p>
              </div>
              <button
                className="text-red-500 hover:text-red-700"
                onClick={() =>
                  setExpenses(expenses.filter((_, i) => i !== index))
                }
              >
                Remove
              </button>
            </div>
            <p className="text-gray-700 mt-2">{expense.description}</p>
            <div className="flex mt-2 space-x-2">
              {expense.receipts.map((receipt, i) => (
                <img
                  key={i}
                  src={receipt}
                  alt="Receipt"
                  className="w-16 h-16 rounded-lg"
                />
              ))}
            </div>
          </div>
        ))}

        {/* Expense Form */}
        <form onSubmit={handleAddExpense}>
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Expense Name
              </label>
              <input
                type="text"
                className="w-full border rounded-lg px-3 py-2"
                placeholder="Select Date"
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Select Category
              </label>
              <input
                type="text"
                className="w-full border rounded-lg px-3 py-2"
                placeholder="Select Category"
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Claim Amount
              </label>
              <input
                type="number"
                className="w-full border rounded-lg px-3 py-2"
                placeholder="Enter Amount"
              />
            </div>
            <div className="col-span-2">
              <label className="block text-gray-700 font-medium mb-2">
                Description
              </label>
              <textarea
                className="w-full border rounded-lg px-3 py-2"
                placeholder="Enter Description"
                rows={3}
              ></textarea>
            </div>
          </div>
          <div className="mb-6">
            <label className="block text-gray-700 font-medium mb-2">
              Upload Receipt
            </label>
            <div className="border-dashed border-2 border-gray-300 rounded-lg p-4 text-center">
              <p className="text-gray-500 mb-2">
                Drag and drop or click to upload
              </p>
              <input
                type="file"
                className="hidden"
                id="upload-receipt"
                onChange={(e) => {
                  const files = e.target.files;
                  console.log("Uploaded files:", files);
                }}
              />
              <label
                htmlFor="upload-receipt"
                className="bg-[#1D3557] text-white px-4 py-2 rounded-lg hover:bg-[#457B9D] cursor-pointer"
              >
                Upload
              </label>
            </div>
          </div>
          <div className="flex justify-between">
            <button
              type="button"
              className="bg-[#A8DADC] text-[#1D3557] px-4 py-2 rounded-lg hover:bg-[#82C4C3]"
            >
              + Add Another Expense
            </button>
            <button
              type="submit"
              className="bg-[#1D3557] text-white px-4 py-2 rounded-lg hover:bg-[#457B9D]"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ExpenseModal;